import {
  FilterOption,
  FilterDefinition,
  CommonSection,
  PageContent,
  PricingDetailData,
  NavigationArticle,
  NavigationGroup,
  ServiceNavigationData
} from './pricing.typing.d.ts'

/**
 * 单个分类节点
 */
export interface CategoryNode {
  id: number;
  name: string;
  originalUrlFragment: string;
  slug: string;
  iconImg: string | null;
  description: string | null;
  level: number;
  sort: number;
  subCategories: CategoryNode[]; // 递归定义
}

/**
 * 完整的接口返回
 */
interface APIResponse<T> {
  data: T;
  success: boolean;
  code: number;
  message: string;
}

export {
  APIResponse,
  FilterOption,
  FilterDefinition,
  CommonSection,
  PageContent,
  PricingDetailData,
  NavigationArticle,
  NavigationGroup,
  ServiceNavigationData
}

