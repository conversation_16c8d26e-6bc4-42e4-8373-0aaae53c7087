<script lang="ts" setup>
const { locale, locales, setLocale } = useI18n()
const cookie = useCookie('i18n_locale', { sameSite: 'lax' })

useHead({
  htmlAttrs: {
    lang: locale.value
  }
})

// 语言选项 - 优先使用动态数据，否则使用静态数据
const languageOptions = computed(() => {
  return locales.value.map(lang => ({
    label: lang.name,
    value: lang.code,
  }))
})

// 切换语言
const changeLanguage = (langCode: string) => {
  cookie.value = langCode;
  setLocale(langCode as "en-us" | "zh-cn").then(() => {
    window.location.reload()
  })
}

</script>

<template>
  <div>
    <USelect v-model="locale" :items="languageOptions" icon="myicons:map" variant="subtle" size="xs"
      class="w-35 bg-transparent text-white hover:bg-transparent cursor-pointer" @update:model-value="changeLanguage" />
  </div>
</template>
