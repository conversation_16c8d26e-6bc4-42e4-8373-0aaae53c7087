// 定义筛选选项的类型
interface FilterOption {
    value: string;
    isDefault: boolean;
    order: number;
    label: string;
}

// 定义筛选器的类型
interface FilterDefinition {
    filterKey: "region" | "service-type";
    filterName: string;
    filterType: 'Dropdown' | 'Tabs';
    order: number;
    options: FilterOption[];
}

// 定义页面中的通用区块类型
interface CommonSection {
    sectionType: 'Banner' | 'ProductDescription' | 'Qa';
    sectionTitle: string;
    content: string;
    sortOrder: number;
}

// 定义页面内容项的类型
interface PageContent {
    filterKey: string;
    content: string;
    sortOrder: number;
}

// 定义整个页面数据的类型
interface PricingDetailData {
    title: string;
    slug: string;
    metaTitle: string;
    metaDescription: string;
    metaKeywords: string;
    pageType: 'Simple' | 'RegionFilter' | 'ComplexFilter';
    pageIcon: string;
    enableFilters: boolean;
    leftNavigationIdentifier: string;
    filterDefinitions: FilterDefinition[];
    commonSections: CommonSection[];
    pageContents: PageContent[];
}

// 定义导航文章项的类型
interface NavigationArticle {
  title: string;
  link: string;
}

// 定义导航分组的类型
interface NavigationGroup {
  group: string;
  articles: NavigationArticle[];
}

// 定义整个服务导航数据的类型
interface ServiceNavigationData {
  serviceDisplayName: string;
  serviceName: string;
  navigation: NavigationGroup[];
}

export {
    FilterOption,
    FilterDefinition,
    CommonSection,
    PageContent,
    PricingDetailData,
    NavigationArticle,
    NavigationGroup,
    ServiceNavigationData
}