<template>
    <USelect v-model="selected" :items="legalOptions" @update:model-value="go" class="w-full mb-2" />
</template>

<script setup lang="ts">
/* ---------------------------------- 类型 ---------------------------------- */
type LegalOption =
    | { category: 'internal'; value: string; label: string }
    | { category: 'external'; value: string; label: string }

/* -------------------------------- 组合式 ---------------------------------- */
const { t } = useI18n()
const route = useRoute()
const localePath = useLocalePath()

/* ---------------------------- 下拉选项 ---------------------------- */
const legalOptions = computed<LegalOption[]>(() => [
    { category: 'internal', value: '/support/legal', label: t('legal.left.overview') },
    { category: 'external', value: 'https://www.21vbluecloud.com/ostpt/', label: t('legal.left.ostpt') },
    { category: 'external', value: 'https://www.21vbluecloud.com/ostpt/', label: t('legal.left.service-terms') },
    { category: 'internal', value: '/support/legal/offer-rate-plans', label: t('legal.left.offer-details') },
    { category: 'external', value: 'https://www.21vbluecloud.com/ostpt/', label: t('legal.left.privacy-statement') },
    { category: 'internal', value: '/support/legal/sla', label: t('legal.left.sla') }
])

/* ---------------------------- 当前值 ---------------------------- */
const selected = computed<string | undefined>(() => {
    // 如果是外链，就不选中任何值
    const internal = legalOptions.value.filter(o => o.category === 'internal')
    const matched = internal.find(o => o.value === route.path)
    return matched ? matched.value : undefined
})

/* ---------------------------- 跳转 ---------------------------- */
function go(val: string) {
    const option = legalOptions.value.find(o => o.value === val)
    if (!option) return

    if (option.category === 'internal') {
        return navigateTo(localePath(option.value))
    }

    // 外链
    window.open(option.value, '_blank', 'noopener')
}
</script>