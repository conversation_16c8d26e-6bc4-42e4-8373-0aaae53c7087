<template>
  <div>
    <Error404 v-if="notfound" />
    <div class="w-9/10 max-lg:w-11/12 mx-auto max-w-[1608px] my-6 grid grid-cols-6 gap-6" v-else>
      <NuxtErrorBoundary>
        <aside class="col-span-6 xl:col-span-1">
          <div class="hidden xl:block bg-[#f4f5f6] py-5">
            <div v-if="leftPending" class="text-center">Loading...</div>
            <PricingDetailsLeftNav :navigation="leftNavData?.data.navigation"
              :service-display-name="leftNavData?.data.serviceDisplayName" v-else-if="leftNavData?.data" />
            <div v-else class="text-center">No data</div>
          </div>
          <!-- 移动端：顶部下拉菜单 -->
          <div class="xl:hidden">
            <PricingDetailsLeftNavMobile :navigation="leftNavData?.data.navigation"
              :service-display-name="leftNavData?.data.serviceDisplayName" v-if="leftNavData?.data" />
          </div>
        </aside>

        <template #error>
          <ErrorCard />
        </template>
      </NuxtErrorBoundary>
      <main class="col-span-6 xl:col-span-5">
        <PricingDetailsBanner :html="banner?.content" />
        <PricingDetailsDescription :html="description?.content" v-if="description?.content" />

        <NuxtErrorBoundary>
          <PricingDetailsFilter :definitions="detail.data.filterDefinitions" v-model:filter-key="filterKey"
            v-if="detail?.data.enableFilters" />
          <template #error>
            <ErrorCard />
          </template>
        </NuxtErrorBoundary>
        <div class="mt-6">
          <SafeHtml :html="content?.data.content" />
        </div>

        <NuxtErrorBoundary>
          <div class="mt-10" v-if="faq">
            <SafeHtml :html="faq?.content" />
          </div>
          <template #error>
            <ErrorCard />
          </template>
        </NuxtErrorBoundary>

      </main>
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { APIResponse, PricingDetailData, ServiceNavigationData } from '~/typing';

const { slug } = useRoute().params

const selectWrapper = ref<HTMLElement>()
const filterKey = ref('all')

const { data: detail } = await useFetch<APIResponse<PricingDetailData>>(`/api/external/PricingPage/${slug}`)

const { data: leftNavData, execute: queryLeftNav, pending: leftPending } = await useFetch<APIResponse<ServiceNavigationData>>('/api/external/Navigation/GetProductNavigation', {
  query: {
    ServiceName: detail.value?.data.leftNavigationIdentifier
  },
  lazy: true
})

const notfound = computed(() => !detail.value?.success)

useHead({
  title: () => detail.value?.data.title,
  meta: [
    { name: "title", content: () => detail.value?.data.metaTitle },
    { name: "keywords", content: () => detail.value?.data.metaKeywords },
    { name: 'description', content: () => detail.value?.data.metaDescription }
  ]
})

const filterParams = computed(() => ({
  slug,
  filter: filterKey.value
}))

const { data: content } = await useFetch<APIResponse<{ content: string; }>>(`/api/external/PricingPage/${slug}/pricing`, {
  params: filterParams,
  lazy: true,
  server: false
})

const banner = computed(() => detail.value?.data.commonSections.find(item => item.sectionType === 'Banner'))

const description = computed(() => detail.value?.data.commonSections.find(item => item.sectionType === 'ProductDescription'))

const faq = computed(() => detail.value?.data.commonSections.find(item => item.sectionType === 'Qa'))

watch(filterKey, () => {
  selectWrapper.value?.scrollIntoView({ behavior: 'smooth', block: 'start' })
})
</script>

<style lang="css" scoped>
@reference "~/assets/css/main.css";

.select-box {
  @apply flex flex-col gap-1 w-1/4;
}
</style>