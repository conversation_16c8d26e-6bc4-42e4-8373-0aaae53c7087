// server/api/external/[...].ts
import {
  getRouterParam,
  getQuery,
  readBody,
  setResponseStatus,
  setHeader,
} from 'h3'
import { wrapHtmlFields } from '~~/server/utils/wrapHtmlFields'

export default defineEventHandler(async (event) => {
  const path   = (getRouterParam(event, '_') || '').replace(/^\/+/, '')
  const target = `http://172.31.226.19:25811/api/${path}`

  const query = getQuery(event)
  const body  = await readBody(event).catch(() => undefined)

  try {
    const res = await $fetch.raw(target, {
      method: event.method,
      query,
      body,
      headers: event.headers,
      timeout: 10000,
    })

    for (const [k, v] of Object.entries(res.headers)) {
      setHeader(event, k, Array.isArray(v) ? v.join(', ') : (v ?? ''))
    }
    setResponseStatus(event, res.status, res.statusText)

    return wrapHtmlFields(res._data)
  } catch (err: any) {
    const response = err.response
    console.error('[proxy error]', target, {
      method: event.method,
      status: response?.status,
      body  : response?._data || err.message,
    })

    setResponseStatus(event, response?.status || 502)
    return {
      message     : 'Proxy Error',
      remoteStatus: response?.status,
      remoteBody  : response?._data || err.message,
    }
  }
})