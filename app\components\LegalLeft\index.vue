<template>
    <div>
        <div class="bg-gray-100 py-2.5">
            <div class="px-4 h-8 leading-8">{{ $t('legal.left.title') }}</div>
            <div class="flex justify-between items-center px-4 h-8 leading-8">
                <span>{{ $t('legal.left.legal-info') }}</span>
                <UIcon name="i-lucide-chevron-down" />
            </div>
            <div>
                <NuxtLinkLocale to="/support/legal" class="link"
                    :class="{ highlight: route.path === '/support/legal' }">{{ $t('legal.left.overview') }}
                </NuxtLinkLocale>
                <NuxtLink to="https://www.21vbluecloud.com/ostpt/" target="_blank" class="link">{{
                    $t('legal.left.ostpt') }}</NuxtLink>
                <NuxtLink to="https://www.21vbluecloud.com/ostpt/" target="_blank" class="link">{{
                    $t('legal.left.service-terms') }}</NuxtLink>
                <NuxtLinkLocale to="/support/legal/offer-rate-plans" class="link" :class="{ highlight: route.path === '/support/legal/offer-rate-plans' }">{{
                    $t('legal.left.offer-details') }}</NuxtLinkLocale>
                <NuxtLink to="https://www.21vbluecloud.com/ostpt/" target="_blank" class="link">{{
                    $t('legal.left.privacy-statement') }}</NuxtLink>
                <NuxtLinkLocale to="/support/legal/sla/" class="link"
                    :class="{ highlight: route.path === '/support/legal/sla' }">{{ $t('legal.left.sla') }}
                </NuxtLinkLocale>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
const route = useRoute()
</script>

<style lang="css" scoped>
@reference "~/assets/css/main.css";

.link {
    @apply block px-4 cursor-pointer text-base text-[#0078D4] h-8 leading-8 hover:bg-[#dbe1ea]
}

.highlight {
    @apply bg-[#0078D4] text-white hover:bg-[#0078D4]
}
</style>