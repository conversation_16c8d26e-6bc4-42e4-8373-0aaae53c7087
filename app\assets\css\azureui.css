@font-face {
    font-family: azure-icon;
    src: url(/fonts/azure-icon.eot);
    src: url(/fonts/azure-icon.eot) format('embedded-opentype'),url(/fonts/azure-icon.ttf) format('truetype'),url(/fonts/azure-icon.woff) format('woff'),url(/fonts/azure-icon.svg) format('svg');
    font-weight: 400;
    font-style: normal
}

[class*=" icon-"],[class^=icon-] {
    font-family: azure-icon;
    speak: none;
    font-style: normal;
    font-weight: 400;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale
}

.icon-navigation-sharp:before {
    content: ""
}

.icon-close-sharp:before {
    content: ""
}

.icon-arrow-down-sharp:before {
    content: ""
}

.icon-search-thick:before {
    content: ""
}

.icon-search:before {
    content: ""
}

.icon-portal:before {
    content: ""
}

.icon-arrow-top:before {
    content: ""
}

.icon-sinaweibo:before {
    content: ""
}

.icon-telephone:before {
    content: ""
}

.icon-triangle:before {
    content: ""
}

.icon-close:before {
    content: ""
}

.icon-navigation:before {
    content: ""
}

.icon-weixin:before {
    content: ""
}

.icon-activity:before {
    content: ""
}

.icon-volume:before {
    content: ""
}

.icon-xiaoice:before {
    content: ""
}

.icon-arrow-right:before {
    content: ""
}

.icon-arrow-right-thin:before {
    content: ""
}

.icon-tick:before {
    content: ""
}

.icon-minus:before {
    content: "\e909";
}

.icon-plus:before {
    content: "\e90a";
}

.icon-info:before {
    content: ""
}

.icon-interrupt:before {
    content: ""
}

.icon-rss:before {
    content: ""
}

.icon-warning:before {
    content: ""
}

.icon-like:before {
    content: ""
}

.icon-view:before {
    content: ""
}

.icon-iphone:before {
    content: ""
}

.icon-support-wrench:before {
    content: ""
}

.icon-qrcode:before {
    content: ""
}

.icon-wrong:before {
    content: ""
}

.icon-correct:before {
    content: ""
}

.icon-tick {
    color: #1fbba6;
    vertical-align: top
}

.common-banner .common-banner-image {
  width: 100%;
  margin: 0 auto;
  padding: 60px 12px;
  background: center right no-repeat;
}

.common-banner .common-banner-image {
  width: 100%;
  padding: 20px;
}

.col-top-banner .common-banner-image img {
  float: left;
  margin-right: 10px;
  margin-top: 5px;
  width: 48px
}

.col-top-banner .common-banner-image h2 {
    font-size: 32px!important;
    line-height: 40px;
    margin-top: 0;
    margin-bottom: 15px;
    font-weight: 500!important
}

.col-top-banner .common-banner-image h2 span {
    display: block;
    font-size: 16px;
    line-height: 24px;
}

 .col-top-banner .common-banner-image h2 small {
    font-size: 100%
}

.col-top-banner .common-banner-image h4 {
    font-weight: 400!important
}

/* faq */
.more-detail {
    position: relative
}

.more-detail i.active {
    -webkit-animation: twinkling 1s ease-in-out
}

.more-detail em,.more-detail i {
    line-height: 24px;
    color: #0078D4
}

.more-detail em {
    position: absolute;
    right: 0;
    top: 6px;
    font-size: 12px;
    margin-left: 10px;
    cursor: pointer;
    font-style: normal
}

.more-detail>ul {
    padding-left: 0
}

.more-detail>ul>li {
    list-style-type: none;
    overflow: hidden;
    cursor: pointer;
    display: flex;
    gap: 4px;
}

.more-detail>ul>li div a {
    color: #1A1A1A!important
}

.more-detail>ul>li div a.active,.more-detail>ul>li div section p a {
    color: #0078D4!important
}

.more-detail>ul>li div a:hover {
    border-bottom: 0!important;
    color: #0078D4!important
}

.more-detail>ul>li div section table {
    margin-bottom: 10px
}

.more-detail>ul>li div section ul {
    margin-bottom: 7px
}

.more-detail>ul>li div section p a:hover {
    border-bottom: 1px solid #0078D4!important
}

.more-detail>ul>li div section {
    display: none
}

.more-detail>ul>li div section.open {
    display: block
}

.more-detail>ul>li div ul li {
    list-style-type: disc
}

.more-detail>ul>li ol li {
    list-style-position: outside
}

.more-detail h3 {
    padding-top: 6px
}

.more-detail .pos-em {
    top: 48px
}

/* table tr th {
    border-top: 2px #ccc solid;
    border-bottom: 2px #ccc solid;
} */

.pure-content .svg {
    width: 50px;
    float: left;
    margin-right: 10px;
}

.pure-content .updatetime {
    color: black;
    text-align: right;
    font-size: 12px;
}

.pure-content .table-wrapper {
    margin-top: 15px;
    overflow-x: auto;
    border: solid 1px #eaebec;
}

.pure-content ul,.pure-content ol {
    list-style: inside;
}

.pure-content ol li {
    line-height: 24px;
    margin-bottom: 10px;
    font-size: 16px
}

.pure-content ul li {
    line-height: 24px;
    margin-bottom: 0;
    padding-bottom: 12px;
    font-size: 16px;
}   

.pure-content p {
    font-size: 16px;
    margin-top: 7px;
    margin-bottom: 7px;
    line-height: 24px;
    color: #1A1A1A;
    word-wrap: break-word
}

.pure-content li:has(p){
    list-style: none;
}

.pure-content li p {    
    display: inline-block;
}

.pure-content table tr td,.pure-content table tr th {
    padding: 15px;
    line-height: 24px;
    word-wrap: break-word;
    /* overflow-wrap: anywhere; */
}

.pure-content table {
    width: 99.9%;
    border: none!important;
    font-size: 16px
}

.pure-content .tags-date .ms-date,.pure-content .tags-date .wacn-date {
    font-size: 12px;
    display: inline-block
}

/* .pure-content table thead {
    border-top: 2px solid #CCC;
    border-bottom: 2px solid #CCC
} */

/* .pure-content table tr {
    border-bottom: 1px solid #CCC */
/* } */

.pure-content table tr th {
    font-weight: 400;
    text-align: left;
    background:#eaebec;
}

.pure-content table tr td sup {
    color: #1a1a1a!important
}

.pure-content table tr:nth-child(even) {
    background-color: #f4f5f6;
}

.pure-content .tags-date {
    color: #6f6f6f;
    margin-bottom: 20px
}

.pure-content .tags-date .ms-date {
    margin-right: 5px
}

.pure-content h1 {
    font-size: 28px;
    line-height: 36px;
    font-weight: 500;
    margin-top: 24px;
}

.pure-content h2 {
    margin-top: 20px;
    margin-bottom: 15px;
    font-weight: 500;
    font-size: 28px;
    line-height: 36px;
    color: #1A1A1A;
}

.pure-content h3 {
    margin-top: 14px;
    margin-bottom: 15px;
    font-weight: 700;
    font-size: 16px;
    line-height: 24px;
    color: #1A1A1A;
}

.pure-content h4,.pure-content h5,.pure-content h6 {
    margin-top: 14px;
    margin-bottom: 15px;
    font-weight: 700;
    font-size: 16px;
    line-height: 24px;
    color: #1A1A1A;
}

.pure-content a {
    color: #006FC3;
}

.pure-content a:active,.pure-content a:hover {
    border-bottom: 1px solid #006FC3
}

.pure-content p {
    font-size: 16px;
    margin-top: 7px;
    margin-bottom: 7px;
    line-height: 24px;
    color: #1A1A1A;
    word-wrap: break-word
}

.pure-content p a {
    margin-left: 6px;
    margin-right: 6px;
    color: #006FC3!important
}

