<template>
    <LegalWrapper>
        <template #default>
            <ContentSections v-for="value in sections" :id="value.id" :key="value.id">
                <div class="mb-6">
                    <h1>{{ value.title }}</h1>
                </div>
            </ContentSections>
        </template>
        <template #right>
            <AnchorNavigation :items="sections" />
        </template>
    </LegalWrapper>
</template>

<script setup>
import AnchorNavigation from '~/components/Anchor/AnchorNavigation.vue';
import ContentSections from '~/components/Anchor/ContentSections.vue';

const sections = [
    { id: 'intro', title: '介绍' },
    { id: 'features', title: '特性' },
    { id: 'pricing', title: '价格' },
    { id: 'contact', title: '联系我们' },
]

</script>
