// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: '2025-07-15',
  devtools: { enabled: false },
  modules: ['@nuxt/eslint', '@nuxt/image', '@nuxt/ui', '@nuxtjs/i18n', 'motion-v/nuxt'],
  css: ['~/assets/css/main.css'],
  ui: {
    fonts: false,
  },
  icon: {
    customCollections: [{
      prefix: 'myicons',
      dir: './app/assets/icons'
    }]
  },
  eslint: {
    config: {
      stylistic: true
    }
  },
  image: {},
  i18n: {
    vueI18n: 'i18n.config.ts',
    defaultLocale: 'zh-cn',
    locales: [
      { code: 'en-us', name: 'English', language: "en-US" },
      { code: 'zh-cn', name: '中文简体', language: 'zh-CN' }
    ],
    strategy: 'prefix_and_default', // 路由策略
    detectBrowserLanguage: {
      useCookie: true,
      cookieKey: 'i18n_locale',
      redirectOn: 'root',
    },
  },
  runtimeConfig: {
    public: {
      apiBase: 'http://172.31.226.19:25811/api'
    }
  }
})
