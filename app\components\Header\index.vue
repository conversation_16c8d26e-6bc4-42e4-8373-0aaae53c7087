<script lang="ts" setup>
import NavMobile from './nav-mobile.vue'
import Navigation from './navigation.vue'

const items = computed(() => ([
  {
    key: 'pricing',
    label: $t('menu.pricing'),
    children: [
      {
        key: 'pricing-list',
        label: $t("menu.ospa"),
        to: '/pricing'
      },
      {
        key: '21vcaepricing',
        label: $t("menu.21vca-e"),
        to: '/21vcaepricing'
      }
    ]
  },
  {
    key: 'documents',
    label: $t('menu.documents'),
    to: 'https://docs.azure.cn/zh-cn/?product=popular',
    target: '_blank'
  },
  {
    key: 'market',
    label: $t('menu.market'),
    to: 'https://market.azure.cn/',
    target: '_blank'
  },
  {
    key: 'support&plan',
    label: $t('menu.support&plan'),
    to: 'https://support.azure.cn/zh-cn/support/plans/',
    target: '_blank'
  }
]))

const showMobelNav = ref(false)

const { locale } = useI18n()

const logoUrl = computed(() => {
  return locale.value === 'zh-cn' ? '/images/logo.svg' : '/images/logo-en.svg'
})

</script>

<template>
  <header class="bg-[#1A1A1A] relative">
    <div class="w-9/10 mx-auto max-w-[1608px] flex justify-between items-center header-padding">
      <div class="flex gap-x-17 items-center">
        <div>
          <NuxtLinkLocale to="/">
            <img :src="logoUrl" alt="logo" class="block w-75 h-5.5">
          </NuxtLinkLocale>
        </div>
        <Navigation :items="items" />
      </div>
      <div>
        <img src="~/assets/images/navigation.svg" alt="mobel nav-icon" class="nav-icon"
          @click="showMobelNav = !showMobelNav">
        <div
          class="text-white text-base border-2 border-white py-[11px] px-[14px] cursor-pointer leading-[normal] hover:bg-white hover:text-black hidden-flex">
          {{ $t('login-button') }}</div>
      </div>
    </div>
    <NavMobile :items="items" :showMobelNav="showMobelNav" />
  </header>
</template>

<style lang="css" scoped>
@reference "~/assets/css/main.css";

.hidden-flex:lang(en-us) {
  @apply hidden 2xl:flex;
}

.hidden-flex:lang(zh-cn) {
  @apply hidden xl:flex;
}

.nav-icon:lang(en-us) {
  @apply 2xl:hidden
}

.nav-icon:lang(zh-cn) {
  @apply xl:hidden
}

.header-padding:lang(en-us) {
  @apply py-3 2xl:py-0
}

.header-padding:lang(zh-cn) {
  @apply py-3 xl:py-0
}
</style>
