<template>
    <nav class="flex bg-gray-100 flex-col py-4 space-y-2">
        <a v-for="(sec, idx) in props.items" :key="sec.id" @click="scrollToSection(sec.id)"
            class="cursor-pointer text-xs px-3 py-1.5 border-l-4 border-transparent transition-colors shrink-0"
            :class="activeIndex === idx ? 'border-blue-500! font-semibold' : 'hover:bg-gray-200'">
            {{ sec.title }}
        </a>
    </nav>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

const props = defineProps({
    items: {
        required: true,
        type: Array<{
            id: string;
            title: string;
        }>,
    }
})

const activeIndex = ref(0)
const isMobile = ref(false)

let ticking = false
let scrollHandler: (() => void) | null = null

const scrollToSection = (id: string) => {
    const el = document.getElementById(id)
    if (!el) return
    el.scrollIntoView({ behavior: 'smooth', block: 'start' })
}

const updateActive = () => {
    const scrollTop = document.scrollingElement?.scrollTop ?? 0
    const offset = 120 // 提前量，可根据实际调整
    for (let i = props.items.length - 1; i >= 0; i--) {
        const el = document.getElementById(props.items[i]!.id)
        if (el && el.offsetTop <= scrollTop + offset) {
            activeIndex.value = i
            break
        }
    }
}

const onScroll = () => {
    if (!ticking) {
        requestAnimationFrame(() => {
            updateActive()
            ticking = false
        })
        ticking = true
    }
}

const updateLayout = () => {
    isMobile.value = window.innerWidth < 768
}

onMounted(() => {
    updateLayout()
    window.addEventListener('resize', updateLayout)

    // 初始化高亮
    updateActive()

    // 监听滚动
    scrollHandler = onScroll
    window.addEventListener('scroll', scrollHandler, { passive: true })
})

onUnmounted(() => {
    window.removeEventListener('resize', updateLayout)
    if (scrollHandler) {
        window.removeEventListener('scroll', scrollHandler)
    }
})
</script>