<template>
  <div>
    <NuxtRouteAnnouncer />
    <NuxtLoadingIndicator />
    <NuxtLayout>
      <NuxtPage />
    </NuxtLayout>
  </div>
</template>

<script lang="ts" setup>
const { locale, t } = useI18n()

// 计算当前语言的 ISO 值（如 zh-CN）
const htmlLang = computed(() => locale.value)

// 动态设置 html lang
useHead({
  title: t('home.meta.title'),
  meta: [
    { name: 'kewords', content: t('home.meta.keywords') },
    { name: 'description', content: t('home.meta.description') }
  ],
  htmlAttrs: {
    lang: htmlLang
  }
})

</script>
