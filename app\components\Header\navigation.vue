<template>
    <div class="hidden-flex gap-6 items-center">
        <div v-for="item in props.items" :key="item.key">
            <div class="relative group h-20 flex items-center">
                <div v-if="!item.to" class=" text-base text-white border-b-2 border-transparent">{{ item.label }}</div>
                <NuxtLinkLocale v-else :to="item.to" :target="item.target"
                    class="block text-base cursor-pointer text-white border-b-2 border-transparent hover:text-[#0078d4] hover:border-b-[#0078d4]">
                    {{ item.label }}
                </NuxtLinkLocale>
                <div v-if="item.children && item.children.length"
                    class="absolute left-0 top-full min-w-full bg-[#2b2a2a] invisible opacity-0 transition ease-in-out duration-300 group-hover:visible group-hover:opacity-100 z-10">
                    <div v-for="child in item.children" :key="child.key">
                        <NuxtLinkLocale :to="child.to"
                            class="block px-4 h-10 leading-10 text-base text-white whitespace-nowrap hover:text-[#0078d4]">
                            {{ child.label }}
                        </NuxtLinkLocale>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
const props = defineProps({
    items: {
        type: Array<{
            key: string;
            label: string;
            to?: string;
            target?: string;
            children?: {
                key: string;
                label: string;
                to: string
            }[]
        }>,
        default: () => []
    }
})
</script>

<style scoped></style>