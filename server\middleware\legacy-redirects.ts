// server/middleware/legacy-redirects.ts
import { defineEventHand<PERSON>, sendRedirect } from 'h3'

// 旧路径 -> 新路径（可放 JSON，也可读 env）
const MAP: Record<string, string> = {
  '/pricing/details/cognitive-services/anomaly-detector/index.html':"/pricing/details/anomaly-detector",
}

export default defineEventHandler(async (event) => {
  let url = event.node.req.url || ''
  // 去掉 query，仅保留 pathname
  const [pathname] = url.split('?', 2)

  // 1. 简单 pathname 映射
  if (MAP[pathname]) {
    return sendRedirect(event, MAP[pathname], 301) // 永久重定向
  }

  // 2. 带 query 的特殊规则
  if (pathname === '/product.php') {
    const { id } = getQuery(event)
    if (id) {
      return sendRedirect(event, `/pr oducts/${id}`, 301)
    }
  }
  // 其余继续走正常流程
})