// plugins/faq-toggle.client.ts
export default defineNuxtPlugin((nuxtApp) => {
    nuxtApp.vueApp.directive('faq', {
        mounted(el, binding) {
            // 从 binding 中获取 $t
            const { $t } = binding.value
            // 1. 按钮：.more-detail 下的第一个 <em>
            const toggleAllBtn = el.querySelector('.more-detail > em') as HTMLElement | null
            // 2. 所有答案区域
            const answers = el.querySelectorAll('section') as NodeListOf<HTMLElement>
            // 3. 所有图标
            const icons = el.querySelectorAll('.icon') as NodeListOf<HTMLElement>
            /* -------- 单条 FAQ 点击 -------- */
            el.querySelectorAll('li').forEach((li: HTMLElement) => {
                const icon = li.querySelector('.icon') as HTMLElement | null
                const title = li.querySelector('a') as HTMLElement | null
                const answer = li.querySelector('section') as HTMLElement | null
                if (!answer) return

                const toggle = () => {
                    const open = !answer.classList.contains('open')
                    answer.classList.toggle('open', open)
                    icon?.classList.toggle('icon-plus', !open)
                    icon?.classList.toggle('icon-minus', open)

                    // 更新全局按钮文字
                    syncGlobalBtn()
                }

                icon?.addEventListener('click', toggle)
                title?.addEventListener('click', toggle)
            })

            /* -------- 全部展开 / 收起 -------- */
            let allOpen = false   // 内部状态，默认折叠

            /* 设置按钮文字 */
            const setBtnText = (open: boolean) => {
                if (!toggleAllBtn) return
                toggleAllBtn.textContent = open
                    ? $t('faq.collapse_all')   // “全部收起” / “Collapse All”
                    : $t('faq.expand_all')     // “全部展开” / “Expand All”
            }

            /* 点击按钮 */
            toggleAllBtn?.addEventListener('click', () => {
                allOpen = !allOpen

                answers.forEach((ans) => ans.classList.toggle('open', allOpen))
                icons.forEach((ic) => {
                    ic.classList.toggle('icon-plus', !allOpen)
                    ic.classList.toggle('icon-minus', allOpen)
                })

                setBtnText(allOpen)
            })

            /* 根据当前展开条数同步按钮文字（单条点击后调用） */
            const syncGlobalBtn = () => {
                const opened = Array.from(answers).filter(a => a.classList.contains('open')).length
                allOpen = opened === answers.length && answers.length > 0
                setBtnText(allOpen)
            }

            /* 初始化文字 */
            setBtnText(false)
        }
    })
})