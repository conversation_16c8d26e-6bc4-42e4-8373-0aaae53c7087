<template>
    <div>
        <div :class="['grid-expand', 'absolute', 'z-999', 'w-full', props.showMobelNav && 'open']">
            <div>
                <div class="bg-black w-full flex flex-col gap-2.5 px-4 py-4 lg:hidden">
                    <div v-for="item in props.items" :key="item.key">
                        <div v-if="!item.to" class="text-sm text-white">{{ item.label }}</div>
                        <NuxtLinkLocale v-else :to="item.to"
                            class="block text-sm text-white">
                            {{ item.label }}
                        </NuxtLinkLocale>
                        <div v-if="item.children && item.children.length" class="w-full -mb-2.5">
                            <div v-for="child in item.children" :key="child.key">
                                <NuxtLinkLocale :to="child.to" class="block px-4 h-10 leading-10 text-sm text-white">
                                    {{ child.label }}
                                </NuxtLinkLocale>
                            </div>
                        </div>
                    </div>
                    <div
                        class="w-full text-sm text-white border-2 border-white mt-2.5 py-[10px] text-center cursor-pointer leading-[normal]">
                        {{ $t('login-button') }}</div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
const props = defineProps({
    showMobelNav: {
        type: Boolean,
        default: false
    },
    items: {
        type: Array<{
            key: string;
            label: string;
            to?: string;
            target?: string;
            children?: {
                key: string;
                label: string;
                to: string
            }[]
        }>,
        default: () => []
    }
})
</script>

<style lang="css" scoped>
.grid-expand {
    display: grid;
    grid-template-rows: 0fr;
    transition: grid-template-rows .35s ease;
}

.grid-expand.open {
    grid-template-rows: 1fr;
}

.grid-expand>div {
    overflow: hidden;
}
</style>