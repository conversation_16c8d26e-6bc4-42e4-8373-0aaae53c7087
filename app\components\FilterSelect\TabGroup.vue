<!-- components/FilterTabGroup.vue -->
<script setup lang="ts">
interface TabItem {
    label: string
    value: string
}

const props = defineProps<{
    modelValue: string | undefined
    items: TabItem[]
}>()

const emit = defineEmits<{
    'update:modelValue': [value: string]
}>()
</script>

<template>
    <div class="flex gap-2">
        <button v-for="item in items" :key="item.value" :class="[
            'px-4 pt-2 pb-1 text-base transition cursor-pointer border-b-4',
            modelValue === item.value
                ? 'border-b-4 border-[#0078D4] bg-[#F4F4F4]'
                : 'bg-[#F4F4F4] border-transparent hover:bg-[#EAEAEA] hover:text-[#0078D4]'
        ]" @click="emit('update:modelValue', item.value)">
            {{ item.label }}
        </button>
    </div>
</template>

<style scoped>
/* 8px 间距 */
button {
    margin-right: 8px;
}

button:last-child {
    margin-right: 0;
}
</style>