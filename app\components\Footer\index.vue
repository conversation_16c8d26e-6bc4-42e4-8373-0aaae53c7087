<script lang="ts" setup>

const columnOne = computed(() => ([
  {
    "title": $t("footer.menu.products-and-pricing"),
    "to": "/pricing",
    "target": "_self"
  },
  {
    "title": $t("footer.menu.azure-hybrid-benefit"),
    "to": "https://www.azure.cn/pricing/hybrid-benefit/",
    "target": "_blank"
  },
  {
    "title": $t("footer.menu.azure-documentation"),
    "to": "https://docs.azure.cn/zh-cn/?product=popular",
    "target": "_blank"
  },
  {
    "title": $t("footer.menu.get-support"),
    "to": "https://support.azure.cn/zh-cn/support/contact",
    "target": "_blank"
  },
  {
    "title": $t("footer.menu.azure-support-plans"),
    "to": "https://support.azure.cn/zh-cn/support/plans",
    "target": "_blank"
  },
  {
    "title": $t("footer.menu.21vca-e-playbook"),
    "to": "https://learn.21vbluecloud.com/zh-cn/home",
    "target": "_blank"
  },
]));

const columnTwo = computed(() => ([
  {
    "title": $t("footer.menu.azure-portal"),
    "to": "https://portal.azure.cn/",
    "target": "_blank"
  },
  {
    "title": $t("footer.menu.enterprise-portal"),
    "to": "https://ea.azure.cn/",
    "target": "_blank"
  },
  {
    "title": $t("footer.menu.account-center"),
    "to": "https://account.windowsazure.cn/Subscriptions",
    "target": "_blank"
  },
  {
    "title": $t("footer.menu.microsoft-azure"),
    "to": "https://azure.microsoft.com/zh-cn/",
    "target": "_blank"
  },
  {
    "title": $t("footer.menu.products-available-by-region"),
    "to": "https://azure.microsoft.com/zh-cn/explore/global-infrastructure/products-by-region/",
    "target": "_blank"
  },
  {
    "title": $t("footer.menu.azure-status"),
    "to": "https://azure.status.microsoft/zh-cn/status",
    "target": "_blank"
  },
  {
    "title": $t("footer.menu.azure-geographies"),
    "to": "https://datacenters.microsoft.com/",
    "target": "_blank"
  },
]));

const columnThree = computed(() => ([
  {
    "title": $t("footer.menu.service-level-agreement-sla"),
    "to": "/support/legal/sla",
    "target": "_self"
  },
  {
    "title": $t("footer.menu.legal-information"),
    "to": "/support/legal",
    "target": "_self"
  },
  {
    "title": $t("footer.menu.icp-filing"),
    "to": "/support/icp",
    "target": "_self"
  },
  {
    "title": $t("footer.menu.public-security-filing"),
    "to": "/support/announcement/public-security-registration",
    "target": "_self"
  },
  {
    "title": $t("footer.menu.privacy-statement"),
    "to": "https://www.21vbluecloud.com/ostpt/",
    "target": "_blank"
  },
  {
    "title": $t("footer.menu.trust-center"),
    "to": "https://www.trustcenter.cn",
    "target": "_blank"
  }
]));

const { locale } = useI18n()

const logoUrl = computed(() => {
  return locale.value === 'zh-cn' ? '/images/logo.svg' : '/images/logo-en.svg'
})
</script>

<template>
  <footer class="bg-[#1A1A1A] text-xs">
    <div class="w-9/10 mx-auto max-w-[1608px] py-6 flex flex-col gap-4 text-white">
      <div class="flex justify-between max-md:gap-2.5">
        <div class="flex-1 flex flex-col gap-4 max-sm:mx-4">
          <div>
            <div class="mb-1 text-gray-400 text-[12px]">{{ $t("footer.followus") }}</div>
            <div class="bg-gray-100 p-1 text-center w-28">
              <img src="~/assets/images/qrcode-a.svg" alt="follow us" class="w-full">
              <div class="text-black text-[10px] mt-1">{{ $t("footer.followus") }}</div>
            </div>
          </div>
          <div>
            <div class="mb-1 text-gray-400 text-[12px]">{{ $t("footer.getsupport") }}</div>
            <div class="bg-gray-100 p-1 text-center w-28">
              <img src="~/assets/images/qrcode-b.svg" alt="get support fast" class="w-full">
              <div class="text-black text-[10px] mt-1">{{ $t("footer.getsupport") }}</div>
            </div>
          </div>
        </div>
        <div class="flex-3 flex flex-col gap-4 md:flex-row">
          <div class="flex-1 flex flex-col gap-4">
            <NuxtLink v-for="item in columnOne" :key="item.to" :to="item.to" :target="item.target">{{ item.title }}
            </NuxtLink>
          </div>
          <div class="flex-1 flex flex-col gap-4">
            <NuxtLink v-for="item in columnTwo" :key="item.to" :to="item.to" :target="item.target">{{ item.title }}
            </NuxtLink>
          </div>
          <div class="flex-1 flex flex-col gap-4">
            <NuxtLink v-for="item in columnThree" :key="item.to" :to="item.to" :target="item.target">{{ item.title }}
            </NuxtLink>
          </div>
        </div>
      </div>
      <div class="mx-auto flex gap-5 items-center max-sm:flex-col max-sm:items-start max-sm:mx-4">
        <div>
          <SelectLang />
        </div>
        <div>
          <NuxtLink to="https://beian.miit.gov.cn/" target="_blank">{{ $t("footer.icp") }}</NuxtLink>
        </div>
        <div class="flex gap-2 items-center">
          <img src="~/assets/images/beian.png">
          <NuxtLink to="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=31011502002224" target="_blank">
            {{ $t("footer.gov") }}</NuxtLink>
        </div>
        <div>
          <NuxtLink to="https://www.azure.cn/support/legal/privacy-statement/">{{ $t("footer.privacy") }}</NuxtLink>
        </div>
        <div>
          <NuxtLink to="https://www.azure.cn/support/legal/privacy-statement/">
            <img :src="logoUrl" class="h-5">
          </NuxtLink>
        </div>
      </div>
    </div>
  </footer>
</template>
