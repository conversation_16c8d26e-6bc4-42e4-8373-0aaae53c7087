<script lang="ts" setup>
import type { CategoryNode, APIResponse } from '~/typing';
// pricing page banner (TailwindCSS)
const { data: category } = await useFetch<APIResponse<CategoryNode[]>>('/api/external/PricingCategory/tree')
console.log(category.value?.data)
</script>

<template>
  <div>
    <div class="w-full bg-[#243a5e]">
      <div class="w-[90%] mx-auto h-[220px] max-w-[1608px]">
        <div class="flex flex-col justify-center h-full text-white">
          <h1 class="text-4xl">{{ $t("pricing.banner.title") }}</h1>
          <p>{{ $t("pricing.banner.subtitle") }}</p>
          <div class="flex gap-x-4 mt-5 flex-wrap">
            <div class="item" >
              <UIcon name="i-lucide-check" />
              {{ $t('pricing.banner.features[0]') }}
            </div>
             <div class="item" >
              <UIcon name="i-lucide-check" />
              {{ $t('pricing.banner.features[1]') }}
            </div>
             <div class="item" >
              <UIcon name="i-lucide-check" />
              {{ $t('pricing.banner.features[2]') }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="w-9/10 mx-auto max-w-[1608px]">
      <div class="mt-10">
        <h2 class="text-3xl mb-6">{{ $t("pricing.title") }}</h2>
        <I18nT keypath="pricing.description.message" tag="p" scope="global">
          <template #cLink>
            <NuxtLinkLocale to="/21vcaepricing" class="text-blue-600">{{ $t("pricing.description.here") }}
            </NuxtLinkLocale>
          </template>
        </I18nT>

      </div>
      <div class="my-8">
        <CategoryColumns v-if="category?.data" :nodes="category?.data" />
      </div>
    </div>
  </div>
</template>

<style scoped lang="css">
@reference "~/assets/css/main.css";


.item {
  @apply flex items-center whitespace-nowrap
}
</style>
