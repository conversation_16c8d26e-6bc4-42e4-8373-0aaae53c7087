<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 840.73 53.86"><defs><style>.cls-1{fill:#fff;}.cls-2{fill:#eb6100;}.cls-3{fill:#8fc31f;}.cls-4{fill:#00a0e9;}.cls-5{fill:#fcc800;}</style></defs><title>dark_bg_en</title><g id="图层_2" data-name="图层 2"><g id="图层_1-2" data-name="图层 1"><path class="cls-1" d="M396.61,31.16q0-6.77,3.76-10a11.37,11.37,0,0,1,7.67-2.7,11,11,0,0,1,8.22,3.29q3.18,3.3,3.19,9.1a16,16,0,0,1-1.42,7.4,10,10,0,0,1-4.1,4.19A11.91,11.91,0,0,1,408,43.9a10.94,10.94,0,0,1-8.27-3.28Q396.61,37.34,396.61,31.16Zm4.25,0c0,3.13.68,5.46,2,7a6.79,6.79,0,0,0,10.26,0c1.36-1.56,2-3.93,2-7.13q0-4.53-2.05-6.85a6.82,6.82,0,0,0-10.25,0Q400.86,26.49,400.86,31.16Z"/><path class="cls-1" d="M424.29,52.69V19h3.76v3.16a9.36,9.36,0,0,1,3-2.78,8.26,8.26,0,0,1,4.06-.93,9.64,9.64,0,0,1,5.51,1.6,9.87,9.87,0,0,1,3.6,4.54A16.56,16.56,0,0,1,445.45,31a16.38,16.38,0,0,1-1.34,6.74,10.25,10.25,0,0,1-3.9,4.59,10.06,10.06,0,0,1-5.39,1.59,7.77,7.77,0,0,1-3.7-.87,8.42,8.42,0,0,1-2.7-2.2V52.69ZM428,31.3q0,4.71,1.9,7a5.85,5.85,0,0,0,4.62,2.25,6,6,0,0,0,4.71-2.33c1.31-1.56,2-4,2-7.22s-.64-5.43-1.92-7a5.79,5.79,0,0,0-4.58-2.32,6,6,0,0,0-4.67,2.47C428.71,25.77,428,28.17,428,31.3Z"/><path class="cls-1" d="M467.11,35.5l4.27.53a10.43,10.43,0,0,1-3.74,5.81,11.27,11.27,0,0,1-7,2.06,11.15,11.15,0,0,1-8.48-3.29q-3.13-3.3-3.14-9.24,0-6.15,3.17-9.55a10.73,10.73,0,0,1,8.22-3.39,10.43,10.43,0,0,1,8,3.32q3.1,3.33,3.1,9.37c0,.24,0,.61,0,1.1H453.32a9.35,9.35,0,0,0,2.27,6.15,6.79,6.79,0,0,0,5.1,2.14,6.31,6.31,0,0,0,3.88-1.2A7.82,7.82,0,0,0,467.11,35.5Zm-13.56-6.68h13.61a8.21,8.21,0,0,0-1.56-4.61,6.34,6.34,0,0,0-5.12-2.39,6.55,6.55,0,0,0-4.78,1.91A7.52,7.52,0,0,0,453.55,28.82Z"/><path class="cls-1" d="M476.52,43.35V19h3.72v3.69a10.12,10.12,0,0,1,2.63-3.42,4.59,4.59,0,0,1,2.65-.82,8.06,8.06,0,0,1,4.25,1.33l-1.43,3.83a5.82,5.82,0,0,0-3-.89,4,4,0,0,0-2.43.81,4.44,4.44,0,0,0-1.54,2.26,16.09,16.09,0,0,0-.68,4.82V43.35Z"/><path class="cls-1" d="M508.13,40.34a15.25,15.25,0,0,1-4.42,2.76,12.88,12.88,0,0,1-4.55.8,8.87,8.87,0,0,1-6.18-2,6.48,6.48,0,0,1-2.16-5,6.62,6.62,0,0,1,.82-3.27,6.77,6.77,0,0,1,2.13-2.37,10,10,0,0,1,3-1.36,28.84,28.84,0,0,1,3.67-.61,37.78,37.78,0,0,0,7.36-1.43q0-.84,0-1.08a4.54,4.54,0,0,0-1.17-3.55,6.9,6.9,0,0,0-4.71-1.4,7.29,7.29,0,0,0-4.3,1,6.23,6.23,0,0,0-2.06,3.61l-4-.55a10.11,10.11,0,0,1,1.82-4.19A7.94,7.94,0,0,1,497,19.29a16.1,16.1,0,0,1,5.53-.86,14.63,14.63,0,0,1,5.07.73A6.7,6.7,0,0,1,510.47,21a6.58,6.58,0,0,1,1.29,2.81,24.37,24.37,0,0,1,.2,3.81v5.51a57.26,57.26,0,0,0,.27,7.29,9,9,0,0,0,1,2.92H509A8.7,8.7,0,0,1,508.13,40.34Zm-.35-9.22A30.43,30.43,0,0,1,501,32.68a15.89,15.89,0,0,0-3.61.82,3.69,3.69,0,0,0-1.63,1.35,3.65,3.65,0,0,0,.68,4.71,5.36,5.36,0,0,0,3.66,1.11,8.5,8.5,0,0,0,4.25-1.05,6.39,6.39,0,0,0,2.73-2.86,10,10,0,0,0,.66-4.13Z"/><path class="cls-1" d="M527.38,39.66l.6,3.65a15.57,15.57,0,0,1-3.12.36,7.13,7.13,0,0,1-3.49-.71,3.92,3.92,0,0,1-1.74-1.87,14.66,14.66,0,0,1-.51-4.88v-14h-3V19h3v-6l4.11-2.48V19h4.15v3.21h-4.15V36.44a7.1,7.1,0,0,0,.22,2.28,1.71,1.71,0,0,0,.71.8,2.74,2.74,0,0,0,1.41.3A14.65,14.65,0,0,0,527.38,39.66Z"/><path class="cls-1" d="M548.11,35.5l4.27.53a10.49,10.49,0,0,1-3.74,5.81,11.3,11.3,0,0,1-7,2.06,11.18,11.18,0,0,1-8.48-3.29q-3.13-3.3-3.13-9.24,0-6.15,3.17-9.55a10.71,10.71,0,0,1,8.21-3.39,10.46,10.46,0,0,1,8,3.32q3.09,3.33,3.1,9.37c0,.24,0,.61,0,1.1H534.32a9.35,9.35,0,0,0,2.27,6.15,6.77,6.77,0,0,0,5.09,2.14,6.29,6.29,0,0,0,3.88-1.2A7.9,7.9,0,0,0,548.11,35.5Zm-13.56-6.68h13.61a8.28,8.28,0,0,0-1.57-4.61,6.32,6.32,0,0,0-5.11-2.39,6.57,6.57,0,0,0-4.79,1.91A7.51,7.51,0,0,0,534.55,28.82Z"/><path class="cls-1" d="M573.38,43.35V40.28a7.6,7.6,0,0,1-6.82,3.62,9.57,9.57,0,0,1-5.36-1.6,10.58,10.58,0,0,1-3.78-4.49,15.44,15.44,0,0,1-1.35-6.62,17.31,17.31,0,0,1,1.22-6.62A9.59,9.59,0,0,1,560.94,20a9.7,9.7,0,0,1,5.44-1.58,8.13,8.13,0,0,1,6.72,3.35V9.71h4.11V43.35ZM560.32,31.19q0,4.68,2,7A6,6,0,0,0,567,40.51a5.82,5.82,0,0,0,4.6-2.22q1.91-2.22,1.9-6.76c0-3.33-.65-5.78-1.93-7.34a5.94,5.94,0,0,0-4.75-2.34,5.71,5.71,0,0,0-4.6,2.25Q560.33,26.35,560.32,31.19Z"/><path class="cls-1" d="M600.58,43.35h-3.84V9.71h4.13v12a8.17,8.17,0,0,1,6.68-3.28,10.31,10.31,0,0,1,4.26.9,8.85,8.85,0,0,1,3.31,2.55,12.33,12.33,0,0,1,2,4,16.35,16.35,0,0,1,.73,5q0,6.25-3.1,9.68a9.67,9.67,0,0,1-7.43,3.42,7.75,7.75,0,0,1-6.77-3.6ZM600.53,31q0,4.38,1.19,6.34a5.9,5.9,0,0,0,10,.83q2-2.34,2-7t-1.9-7a5.74,5.74,0,0,0-4.57-2.28,6,6,0,0,0-4.69,2.36Q600.54,26.52,600.53,31Z"/><path class="cls-1" d="M622.72,52.74l-.46-3.88a9.48,9.48,0,0,0,2.37.37,4.58,4.58,0,0,0,2.2-.46,3.9,3.9,0,0,0,1.36-1.29,20.24,20.24,0,0,0,1.26-3.07c.07-.23.2-.57.37-1L620.57,19H625l5.07,14.11q1,2.69,1.77,5.65a54.21,54.21,0,0,1,1.7-5.56L638.77,19h4.13l-9.28,24.78a48.49,48.49,0,0,1-2.31,5.54,8.16,8.16,0,0,1-2.53,3,6,6,0,0,1-3.39,1A8.46,8.46,0,0,1,622.72,52.74Z"/><path class="cls-1" d="M680,39.38v4H657.79a7.38,7.38,0,0,1,.48-2.87A15.47,15.47,0,0,1,661,36a42.71,42.71,0,0,1,5.41-5.1,45.56,45.56,0,0,0,7.41-7.12,8.53,8.53,0,0,0,1.93-5A5.51,5.51,0,0,0,674,14.67,6.33,6.33,0,0,0,669.41,13a6.45,6.45,0,0,0-4.78,1.79,6.75,6.75,0,0,0-1.81,5l-4.25-.44a10.47,10.47,0,0,1,3.28-7.24,11.22,11.22,0,0,1,7.65-2.49,10.64,10.64,0,0,1,7.66,2.68A8.8,8.8,0,0,1,680,18.91a10.1,10.1,0,0,1-.83,4A15.6,15.6,0,0,1,676.42,27a66.84,66.84,0,0,1-6.37,5.92q-3.72,3.12-4.78,4.23a13.72,13.72,0,0,0-1.74,2.24Z"/><path class="cls-1" d="M700,43.35h-4.13V17A21.3,21.3,0,0,1,692,19.87,28.71,28.71,0,0,1,687.63,22V18a24.93,24.93,0,0,0,6.06-3.94,15.63,15.63,0,0,0,3.67-4.5H700Z"/><path class="cls-1" d="M721.89,43.35l-13-33.64h4.82l8.74,24.44c.71,2,1.29,3.79,1.77,5.51Q725,36.9,726,34.15l9.09-24.44h4.55L726.46,43.35Z"/><path class="cls-1" d="M742.27,14.46V9.71h4.14v4.75Zm0,28.89V19h4.14V43.35Z"/><path class="cls-1" d="M768.6,40.34a15.25,15.25,0,0,1-4.42,2.76,12.84,12.84,0,0,1-4.55.8,8.87,8.87,0,0,1-6.18-2,6.48,6.48,0,0,1-2.16-5,6.81,6.81,0,0,1,3-5.64,10,10,0,0,1,3-1.36,28.84,28.84,0,0,1,3.67-.61,38.07,38.07,0,0,0,7.37-1.43c0-.56,0-.92,0-1.08a4.54,4.54,0,0,0-1.17-3.55,6.9,6.9,0,0,0-4.71-1.4,7.29,7.29,0,0,0-4.3,1,6.17,6.17,0,0,0-2.05,3.61l-4-.55a10,10,0,0,1,1.81-4.19,7.94,7.94,0,0,1,3.65-2.45,16.1,16.1,0,0,1,5.53-.86,14.63,14.63,0,0,1,5.07.73A6.7,6.7,0,0,1,770.94,21a6.58,6.58,0,0,1,1.29,2.81,24.37,24.37,0,0,1,.2,3.81v5.51a57.26,57.26,0,0,0,.27,7.29,9,9,0,0,0,1,2.92h-4.31A8.7,8.7,0,0,1,768.6,40.34Zm-.34-9.22a30.54,30.54,0,0,1-6.75,1.56,15.89,15.89,0,0,0-3.61.82,3.69,3.69,0,0,0-1.63,1.35,3.65,3.65,0,0,0,.68,4.71,5.37,5.37,0,0,0,3.66,1.11,8.5,8.5,0,0,0,4.25-1.05,6.39,6.39,0,0,0,2.73-2.86,10,10,0,0,0,.67-4.13Z"/><path class="cls-1" d="M778.84,43.35V19h3.71v3.46a8.78,8.78,0,0,1,7.76-4,10.13,10.13,0,0,1,4.05.79,6.38,6.38,0,0,1,2.77,2.08,7.86,7.86,0,0,1,1.28,3.05,22.81,22.81,0,0,1,.23,4v15h-4.13V28.52a11.13,11.13,0,0,0-.48-3.77,4,4,0,0,0-1.71-2,5.51,5.51,0,0,0-2.88-.74,6.74,6.74,0,0,0-4.56,1.67Q783,25.36,783,30V43.35Z"/><path class="cls-1" d="M821.66,35.5l4.27.53a10.43,10.43,0,0,1-3.74,5.81,11.3,11.3,0,0,1-7,2.06,11.18,11.18,0,0,1-8.48-3.29q-3.13-3.3-3.13-9.24,0-6.15,3.17-9.55A10.72,10.72,0,0,1,815,18.43a10.46,10.46,0,0,1,8,3.32q3.1,3.33,3.1,9.37c0,.24,0,.61,0,1.1H807.87a9.35,9.35,0,0,0,2.27,6.15,6.78,6.78,0,0,0,5.1,2.14,6.28,6.28,0,0,0,3.87-1.2A7.83,7.83,0,0,0,821.66,35.5ZM808.1,28.82h13.61a8.36,8.36,0,0,0-1.56-4.61A6.34,6.34,0,0,0,815,21.82a6.58,6.58,0,0,0-4.79,1.91A7.56,7.56,0,0,0,808.1,28.82Z"/><path class="cls-1" d="M840.14,39.66l.59,3.65a15.57,15.57,0,0,1-3.12.36,7.13,7.13,0,0,1-3.49-.71,4,4,0,0,1-1.74-1.87,14.66,14.66,0,0,1-.51-4.88v-14h-3V19h3v-6L836,10.46V19h4.16v3.21H836V36.44a7.42,7.42,0,0,0,.22,2.28,1.77,1.77,0,0,0,.71.8,2.74,2.74,0,0,0,1.41.3A14.78,14.78,0,0,0,840.14,39.66Z"/><rect class="cls-2" width="25.6" height="25.6"/><rect class="cls-3" x="28.26" width="25.6" height="25.6"/><rect class="cls-4" y="28.26" width="25.6" height="25.6"/><rect class="cls-5" x="28.26" y="28.26" width="25.6" height="25.6"/><path class="cls-1" d="M281.57,10.69l12.2,32.43h-6.19l-2.87-8H271.94l-2.76,8H263l12.21-32.43Zm-3.35,6-4.79,13.65h9.72l-4.77-13.65Z"/><polygon class="cls-1" points="296.52 19.87 315.08 19.87 315.08 22.07 302.89 38.78 315.15 38.78 315.15 43.12 295.66 43.12 295.66 40.52 307.66 24.21 296.52 24.21 296.52 19.87"/><path class="cls-1" d="M338.43,19.87V43.12h-5.52V40.06h-.09a7.39,7.39,0,0,1-2.9,2.48,9.25,9.25,0,0,1-4.08.89,8,8,0,0,1-6.17-2.29q-2.12-2.3-2.11-7.13V19.87h5.54V33.35a7.11,7.11,0,0,0,1.16,4.42,4.2,4.2,0,0,0,3.52,1.48,4.67,4.67,0,0,0,3.73-1.64,6.37,6.37,0,0,0,1.4-4.28V19.87Z"/><path class="cls-1" d="M354.9,19.49a7,7,0,0,1,1.18.09,5.06,5.06,0,0,1,.88.23v5.54a4.49,4.49,0,0,0-1.26-.6,6.12,6.12,0,0,0-2-.29,4.26,4.26,0,0,0-3.4,1.7q-1.4,1.69-1.39,5.22V43.12h-5.47V19.87h5.47v3.67H349a6.45,6.45,0,0,1,2.26-3,6.18,6.18,0,0,1,3.64-1.07"/><path class="cls-1" d="M376.65,37.26v4.5a11.73,11.73,0,0,1-3.55,1.39,20,20,0,0,1-4.68.53q-5.31,0-8.26-3.14c-2-2.1-3-5-3-8.75a12.79,12.79,0,0,1,3.15-8.9,10.31,10.31,0,0,1,8-3.49,9.59,9.59,0,0,1,7.49,3c1.79,2,2.68,4.68,2.68,8.15v2.65H362.61q.36,3.49,2.25,4.89a8,8,0,0,0,4.87,1.39,11.71,11.71,0,0,0,3.76-.6,12.12,12.12,0,0,0,3.16-1.57m-3.5-8.12A6.05,6.05,0,0,0,371.89,25a4.44,4.44,0,0,0-3.53-1.43A5.08,5.08,0,0,0,364.83,25a7.22,7.22,0,0,0-2.08,4.14Z"/><path class="cls-1" d="M105,10.7V43.12H99.32V17.71h-.09L89.17,43.12H85.44L75.13,17.71h-.07V43.12h-5.2V10.7h8.07l9.32,24h.13l9.84-24Zm4.71,2.46a3,3,0,0,1,1-2.27,3.48,3.48,0,0,1,4.74,0,3.08,3.08,0,0,1,1,2.25,2.93,2.93,0,0,1-1,2.24,3.35,3.35,0,0,1-2.37.9,3.29,3.29,0,0,1-2.36-.91,3,3,0,0,1-1-2.23m6,30h-5.47V19.88h5.47Zm16.6-4a7.52,7.52,0,0,0,2.69-.57,11.33,11.33,0,0,0,2.71-1.49v5.09a11.25,11.25,0,0,1-3,1.13,16.63,16.63,0,0,1-3.65.38,11.05,11.05,0,0,1-11.56-11.53,13.26,13.26,0,0,1,3.28-9.23q3.28-3.63,9.29-3.63a12.86,12.86,0,0,1,3.11.39,10.5,10.5,0,0,1,2.5.92v5.24a11.45,11.45,0,0,0-2.59-1.43,7.43,7.43,0,0,0-2.7-.51,6.84,6.84,0,0,0-5.22,2.1,7.9,7.9,0,0,0-2,5.68,7.52,7.52,0,0,0,1.91,5.49,6.9,6.9,0,0,0,5.19,2m21-19.65a6.87,6.87,0,0,1,1.17.09,4.83,4.83,0,0,1,.88.23v5.54a4.41,4.41,0,0,0-1.25-.6,6.17,6.17,0,0,0-2-.29,4.24,4.24,0,0,0-3.4,1.7q-1.39,1.69-1.39,5.22V43.12H141.8V19.88h5.48v3.66h.09a6.38,6.38,0,0,1,2.26-3,6.16,6.16,0,0,1,3.64-1.07m2.35,12.34q0-5.76,3.26-9.13t9-3.37q5.45,0,8.51,3.25t3.06,8.76a12.37,12.37,0,0,1-3.25,9,11.8,11.8,0,0,1-8.86,3.34,11.56,11.56,0,0,1-8.58-3.18q-3.18-3.16-3.18-8.67m5.7-.18A8.34,8.34,0,0,0,163,37.22a5.88,5.88,0,0,0,4.72,1.92,5.52,5.52,0,0,0,4.55-1.92,8.91,8.91,0,0,0,1.56-5.7,8.6,8.6,0,0,0-1.62-5.67,5.63,5.63,0,0,0-4.53-1.91A5.72,5.72,0,0,0,163,26a8.67,8.67,0,0,0-1.66,5.7M187.63,26a2.33,2.33,0,0,0,.75,1.84,12.53,12.53,0,0,0,3.3,1.69,11.09,11.09,0,0,1,4.6,2.95,6.1,6.1,0,0,1,1.32,4,6.37,6.37,0,0,1-2.52,5.27,10.69,10.69,0,0,1-6.82,2,16.59,16.59,0,0,1-3.19-.35,14.32,14.32,0,0,1-3-.89V37.06a13.93,13.93,0,0,0,3.21,1.65,9.64,9.64,0,0,0,3.12.61,5.53,5.53,0,0,0,2.74-.52,1.86,1.86,0,0,0,.88-1.74,2.38,2.38,0,0,0-.92-1.91,13.48,13.48,0,0,0-3.47-1.8,10.57,10.57,0,0,1-4.29-2.85,6.2,6.2,0,0,1-1.27-4,6.38,6.38,0,0,1,2.5-5.17,10,10,0,0,1,6.48-2,15.71,15.71,0,0,1,2.73.27,11.9,11.9,0,0,1,2.54.7v5.2a11.55,11.55,0,0,0-2.54-1.24,8.34,8.34,0,0,0-2.87-.52,4.16,4.16,0,0,0-2.43.61,1.92,1.92,0,0,0-.87,1.67M200,31.83q0-5.76,3.26-9.13t9-3.37q5.44,0,8.51,3.25a12.24,12.24,0,0,1,3.07,8.76,12.37,12.37,0,0,1-3.26,9q-3.25,3.34-8.86,3.34a11.59,11.59,0,0,1-8.58-3.18Q200,37.34,200,31.83m5.7-.18a8.34,8.34,0,0,0,1.65,5.57A5.88,5.88,0,0,0,212,39.14a5.52,5.52,0,0,0,4.55-1.92,8.91,8.91,0,0,0,1.56-5.7,8.6,8.6,0,0,0-1.62-5.67A5.63,5.63,0,0,0,212,23.94a5.72,5.72,0,0,0-4.67,2,8.67,8.67,0,0,0-1.66,5.7M242,24.35h-8.15V43.12h-5.54V24.35H224.4V19.88h3.89V16.64a8,8,0,0,1,2.38-6,8.37,8.37,0,0,1,6.12-2.34,13.18,13.18,0,0,1,1.76.1,6.67,6.67,0,0,1,1.36.31v4.72a6.24,6.24,0,0,0-.95-.38,4.93,4.93,0,0,0-1.56-.23,3.3,3.3,0,0,0-2.65,1.08,4.76,4.76,0,0,0-.92,3.17v2.81H242V14.65L247.47,13v6.9H253v4.47h-5.54V35.23a4.59,4.59,0,0,0,.78,3,3.14,3.14,0,0,0,2.45.88,3.65,3.65,0,0,0,1.14-.23,5.29,5.29,0,0,0,1.17-.54v4.52a6.09,6.09,0,0,1-1.73.54,11.56,11.56,0,0,1-2.39.25,6.76,6.76,0,0,1-5.18-1.84c-1.16-1.23-1.73-3.08-1.73-5.55Z"/></g></g><!-- Code injected by live-server -->
<script>
	// <![CDATA[  <-- For SVG support
	if ('WebSocket' in window) {
		(function () {
			function refreshCSS() {
				var sheets = [].slice.call(document.getElementsByTagName("link"));
				var head = document.getElementsByTagName("head")[0];
				for (var i = 0; i < sheets.length; ++i) {
					var elem = sheets[i];
					var parent = elem.parentElement || head;
					parent.removeChild(elem);
					var rel = elem.rel;
					if (elem.href && typeof rel != "string" || rel.length == 0 || rel.toLowerCase() == "stylesheet") {
						var url = elem.href.replace(/(&|\?)_cacheOverride=\d+/, '');
						elem.href = url + (url.indexOf('?') >= 0 ? '&' : '?') + '_cacheOverride=' + (new Date().valueOf());
					}
					parent.appendChild(elem);
				}
			}
			var protocol = window.location.protocol === 'http:' ? 'ws://' : 'wss://';
			var address = protocol + window.location.host + window.location.pathname + '/ws';
			var socket = new WebSocket(address);
			socket.onmessage = function (msg) {
				if (msg.data == 'reload') window.location.reload();
				else if (msg.data == 'refreshcss') refreshCSS();
			};
			if (sessionStorage && !sessionStorage.getItem('IsThisFirstTime_Log_From_LiveServer')) {
				console.log('Live reload enabled.');
				sessionStorage.setItem('IsThisFirstTime_Log_From_LiveServer', true);
			}
		})();
	}
	else {
		console.error('Upgrade your browser. This Browser is NOT supported WebSocket for Live-Reloading.');
	}
	// ]]>
</script>
</svg>