<script lang="ts" setup>
import { onMounted, ref } from 'vue'
import { useI18n } from 'vue-i18n'
const { locale } = useI18n()
const loading = ref(false);
const error = ref();
const tableData = ref();

const json = ref();

const pageNo = ref(1);
const total = ref(0);
const location = ref();
const locations = ref();
const skuName = ref();
const skuNames = ref();
const type = ref();
const types = ref();

const dateForDownloadLink = ref(0);
const linkForDownloadLink = ref(0);

const serviceFamilys = ref();
const serviceFamily = ref();
const serviceNames = ref();
const serviceName = ref();
const productNames = ref();
const productName = ref();

const columns = [
  {
    accessorKey: 'serviceFamily',
    header: $t('ncei.serviceFamily'),
    cell: ({ row }: any) => `${row.original.serviceFamily}`
  },
  {
    accessorKey: 'serviceName',
    header: $t('ncei.serviceName'),
    cell: ({ row }: any) => `${row.original.serviceName}`
  },
  {
    accessorKey: 'productName',
    header: $t('ncei.productName'),
    cell: ({ row }: any) => `${row.original.productName}`
  },
  {
    accessorKey: 'location',
    header: $t('ncei.location'),
    cell: ({ row }: any) => `${row.original.location}`
  },
  {
    accessorKey: 'skuName',
    header: $t('ncei.skuName'),
    cell: ({ row }: any) => `${row.original.skuName}`
  },
  {
    accessorKey: 'type',
    header: $t('ncei.type'),
    cell: ({ row }: any) => `${row.original.type}`
  },
  {
    accessorKey: 'term',
    header: $t('ncei.term'),
    cell: ({ row }: any) => `${row.original.term}`
  },
  {
    accessorKey: 'meterName',
    header: $t('ncei.meterName'),
    cell: ({ row }: any) => `${row.original.meterName}`
  },
  {
    accessorKey: 'unitOfMeasure',
    header: $t('ncei.unitOfMeasure'),
    cell: ({ row }: any) => `${row.original.unitOfMeasure}`
  },
  {
    accessorKey: 'price',
    // header: $t('ncei.price'),
    header: () => {
      return h('div', {}, [h('div', $t('ncei.price.a')), h('div', $t('ncei.price.b'))])
    },
    cell: ({ row }: any) => `${row.original.price}`
  },
  {
    accessorKey: 'effectiveStartDate',
    header: $t('ncei.effectiveStartDate'),
    cell: ({ row }: any) => `${row.original.effectiveStartDate}`
  }
]

const $fetchData = async () => {
  try {
    const response = await $fetch.raw(`https://ncepricingapi.21vbluecloud.com/RetailPrice/GetProductLevelFilterResources/${locale.value === "zh-cn" ? "1" : "0"}`);
    json.value = response._data;
    const arr: any[] = []
    json.value['serviceFamily'].forEach(({ serviceFamilyLabel }: any) => {
      arr.push(serviceFamilyLabel);
    });
    serviceFamilys.value = arr;
  } catch (error) {
    console.error('请求错误:', error);
    alert('获取数据失败');
  } finally {
  }
};

const getSubLevelFilterResources = async () => {
  try {
    const response = await $fetch.raw('https://ncepricingapi.21vbluecloud.com/RetailPrice/GetSubLevelFilterResources', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        language: locale.value === "zh-cn" ? 1 : 0,
        serviceFamily: serviceFamily.value,
        serviceName: serviceName.value,
        productName: productName.value
      })
    });

    const { location, skuName, type }: any = response._data;
    locations.value = location;
    skuNames.value = skuName;
    types.value = type;

    // location.value = locations.value[0];
    // skuName.value = skuNames.value[0];
    // type.value = types.value[0];
  } catch (error) {
    console.error('请求错误:', error);
    alert('获取数据失败');
  } finally {
  }
};

const getDownloadLink = async () => {

  try {
    loading.value = true;
    error.value = null;
    const response = await $fetch.raw(`https://ncepricingapi.21vbluecloud.com/RetailPrice/GetLatestPriceListFile/${locale.value === "zh-cn" ? 1 : 0}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    });


    const { fileName, fileUri, createdDate }: any = response._data;
    dateForDownloadLink.value = createdDate;
    linkForDownloadLink.value = fileUri;

  } catch (err: any) {
    error.value = `获取数据失败: ${err.message}`;
    console.error('请求错误:', err);
  } finally {

  }
}

const $fetchPostData = async () => {
  try {
    loading.value = true;
    error.value = null;
    const response = await $fetch.raw('https://ncepricingapi.21vbluecloud.com/RetailPrice/QueryRetailPrice', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        language: locale.value === "zh-cn" ? 1 : 0,
        pageNo: pageNo.value,
        pageSize: 20,
        serviceFamily: serviceFamily.value || "计算",
        serviceName: serviceName.value || "",
        productName: productName.value || "",
        location: location.value || "中国北部 3",
        skuName: skuName.value || "",
        type: type.value || "Reservation"
      })
    });

    const { priceData, total }: any = response._data;
    tableData.value = priceData;
    total.value = total;

  } catch (err: any) {
    error.value = `获取数据失败: ${err.message}`;
    console.error('请求错误:', err);
  } finally {
    loading.value = false;
  }
};

const handleServiceFamilyChange = (value: any) => {
  serviceFamily.value = value;
  json.value["serviceFamily"].forEach(({ serviceFamilyLabel, serviceName }: any) => {
    if (serviceFamilyLabel === value) {
      const arr: any[] = [];
      serviceName.forEach(({ serviceNameLabel }: any) => {
        arr.push(serviceNameLabel);
      })
      serviceNames.value = arr;
    }
  });
}
const handleServiceNameChange = (value: any) => {
  serviceName.value = value;
  json.value["serviceFamily"].forEach(({ serviceFamilyLabel, serviceName }: any) => {
    if (serviceFamilyLabel === serviceFamily.value) {
      serviceName.forEach(({ serviceNameLabel, productName }: any) => {
        if (value === serviceNameLabel) {
          const arr: any[] = [];
          productName.forEach(({ productNameLabel }: any) => {
            arr.push(productNameLabel);
          })
          productNames.value = arr;
        }
      })
    }
  });
}

const handleProductNameChange = (value: any) => {
  productName.value = value;
  console.log(serviceFamily.value, serviceName.value, productName.value);
  getSubLevelFilterResources();
}

const search = () => {
  console.log(serviceFamily.value, serviceName.value, productName.value,
    location.value, skuName.value, type.value);
  $fetchPostData();
}

const reset = () => {

  serviceFamily.value = null;
  serviceName.value = null;
  productName.value = null;
  location.value = "";
  skuName.value = "";
  type.value = "";

  // serviceFamilys.value = [];
  serviceNames.value = [];
  productNames.value = [];
  locations.value = [];
  skuNames.value = [];
  types.value = [];
}

watch(pageNo, () => {
  console.log(pageNo.value);
  $fetchPostData();
})

onMounted(() => {
  $fetchData();
  $fetchPostData();
  getDownloadLink();
})

</script>
<style lang="css" scoped>
.banner {
  background-color: #243a5e;
  color: #fff;
}

.flow {
  display: flex;
  justify-content: space-between;
}

.item {
  flex: 1;
}

.f1 {
  flex: 1;
}

.center {
  align-items: center;
}

label {
  min-width: 80px;
}
</style>
<template>

  <div class="banner">
    <div class="w-[90%] mx-auto max-w-[1608px] pt-10 pb-10">
      <h1 class="text-4xl">{{ $t("ncei.title") }}</h1>
      <p class="mt-5">{{ $t("ncei.subTitle") }}</p>
      <p class="mt-0">{{ $t("ncei.subTitle2") }}</p>
    </div>
  </div>

  <div class="w-[90%] mx-auto max-w-[1608px] mt-10" v-if="locale === 'zh-cn'">
    <p>新商务模式（21VCA-E）以高效灵活的创新模式，满足您的企业在数字化转型中的新需求，助力企业提韧增效，加速商业成功。新商务模式引入了新的优惠和条款，使您在购买方式和时间方面有更大的选择和灵活性。
      <a target="_blank" class="underline text-blue-500" href="https://learn.21vbluecloud.com/zh-cn/home">点击这里</a>
      了解关于21VCA-E的更多详情。
    </p>
    <p class="mt-5"><b>适用于计算的Azure节省计划：</b>适用于计算的Azure节省计划：<a target="_blank"
        href="https://docs.azure.cn/zh-cn/cost-management-billing/savings-plan/savings-plan-compute-overview"
        class="underline text-blue-500">点击这里</a>了解更多。
      Azure节省计划不适用于中国东部和中国北部。</p>
    <p class="mt-5"><b>Azure预留：</b>Azure 预留项通过承诺多种产品的一年期或三年期计划，帮助您节省资金。<a target="_blank"
        href="https://docs.azure.cn/zh-cn/cost-management-billing/reservations/save-compute-costs-reservations"
        class="underline text-blue-500">点击这里</a>了解更多。
    </p>
  </div>

  <div class="w-[90%] mx-auto max-w-[1608px] mt-10" v-if="locale === 'en-us'">
    <p>The 21VCA-E supports your company’s digital transformation with an efficient and flexible innovative model, and
      help you improve resilience, increase efficiency, and accelerate business growth. The new offers and terms provide
      you with greater choice and flexibility in how and when to buy. Please <a target="_blank"
        href="https://learn.21vbluecloud.com/en-us/home" class="underline text-blue-500">Click HERE</a> to know more
      about the
      21VAC-E.</p>
    <p class="mt-5"><b>Azure Savings Plan for compute introduction: </b>Azure savings plan for compute is a flexible
      pricing model.
      It provides savings up to 65% off pay-as-you-go pricing when you commit to spend a fixed hourly amount on compute
      services for one or three years. <a target="_blank"
        href="https://docs.azure.cn/en-us/cost-management-billing/savings-plan/savings-plan-compute-overview"
        class="underline text-blue-500">Click
        HERE</a> to know more. Azure Savings Plan is not applicable to China East and China North.</p>
    <p class="mt-5"><b>Azure reservation introduction: </b>Azure Reservations help you save money by committing to
      one-year or
      three-year plans for multiple products. <a target="_blank"
        href="https://docs.azure.cn/en-us/cost-management-billing/reservations/save-compute-costs-reservations"
        class="underline text-blue-500">Click
        HERE</a> to know more. </p>
  </div>

  <div class="w-[90%] mx-auto max-w-[1608px] mt-10">
    <div class="flex center justify-between space-x-4">
      <div class="item flow center space-x-4">
        <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t("ncei.serviceFamily") }}</label>
        <USelectMenu class="w-full" :model-value="serviceFamily" :items="serviceFamilys" size="lg"
          @update:modelValue="handleServiceFamilyChange" />
      </div>
      <div class="item flow center space-x-4">
        <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t("ncei.serviceName") }}</label>
        <USelectMenu class="w-full" :model-value="serviceName" :items="serviceNames" size="lg"
          @update:modelValue="handleServiceNameChange" />
      </div>
      <div class="item flow center space-x-4">
        <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t("ncei.productName") }}</label>
        <USelectMenu class="w-full" :model-value="productName" :items="productNames" size="lg"
          @update:modelValue="handleProductNameChange" />
      </div>
    </div>

    <div class="flex center justify-between space-x-4 mt-5">
      <div class="item flow center space-x-4">
        <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t("ncei.location") }}</label>
        <USelect v-model="location" :items="locations" class="w-full" size="lg" />
      </div>
      <div class="item flow center space-x-4">
        <label class="block text-sm font-medium text-gray-700 mb-1">SKU</label>
        <USelect v-model="skuName" :items="skuNames" class="w-full" size="lg" />
      </div>
      <div class="item flow center space-x-4">
        <label class="block text-sm font-medium text-gray-700 mb-1">{{ $t("ncei.type") }}</label>
        <USelect v-model="type" :items="types" class="w-full" size="lg" />
      </div>
    </div>

    <div class="space-x-4 mt-5 float-right">
      <button @click="search"
        class="px-4 py-2 bg-blue-600 text-white rounded-lg cursor-pointer hover:bg-blue-500 transition-colors">{{
          $t("ncei.search") }}</button>
      <button @click="reset"
        class="px-4 py-2 bg-gray-100 border border-blue-600 text-blue-600 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors">{{
          $t("ncei.reset") }}</button>
    </div>
    <div class="overflow-x-auto mt-5 w-full">
      <div class="last space-x-4"><span>{{ $t("ncei.lastUpdate") }}：{{ new Intl.DateTimeFormat('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      }).format(new Date(dateForDownloadLink)) }}</span><a class="underline text-blue-500"
          :href="String(linkForDownloadLink)">{{ $t("ncei.Export&Download") }}</a>
      </div>
    </div>
    <div class="overflow-x-auto w-full">
      <UTable :data="tableData" :columns="columns" class="w-400" />
    </div>
    <div class="overflow-x-auto mt-5 mb-10 w-full">
      <div class="space-x-4 mt-5 float-right">
        <UPagination v-model:page="pageNo" :v-modl="pageNo" active-color="neutral" :total="total" />
      </div>
    </div>
  </div>
</template>
