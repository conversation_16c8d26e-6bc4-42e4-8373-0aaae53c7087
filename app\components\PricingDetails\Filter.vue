<script setup lang="ts">
import type { FilterDefinition } from '~/typing'

interface Props {
    definitions: FilterDefinition[]
    filterKey: string
}

const props = defineProps<Props>()
const emit = defineEmits<{
    'update:filterKey': [key: string]
}>()

/* 本地可写 ref：读 props，写 emit */
const localKey = computed<string>({
    get: () => props.filterKey,
    set: val => emit('update:filterKey', val),
})
</script>

<template>
    <div>
        <div ref="selectWrapper" class="h-0 w-0 mt-5"></div>
        <div v-if="definitions?.length" class="mb-4 py-2.5 flex max-xl:flex-col gap-6 sticky top-0 z-10 bg-white">
            <FilterSelect :definitions="definitions" v-model:filter-key="localKey" />
        </div>
    </div>
</template>