import { load } from 'cheerio'
import { decode } from 'html-entities'

/**
 * 递归遍历对象/数组，把匹配规则的值当作 HTML 片段处理
 * @param data   任意 JSON 结构
 * @param test   判断值是否需要包装的判断函数
 * @returns      同结构的新对象（已替换掉 HTML 字段）
 */
export function wrapHtmlFields(
    node: any,
): any {
    if (typeof node === 'string') {
        const decoded = decode(node)
        if (decoded.includes('<table')) {
            const $ = load(decoded)
            $('table').each((_, t) => { $(t).wrap('<div class="table-wrapper"></div>') })
            return $.html()
        }
        return node
    }
    if (Array.isArray(node)) return node.map(wrapHtmlFields)
    if (node && typeof node === 'object') {
        const res: any = {}
        Object.entries(node).forEach(([k, v]) => (res[k] = wrapHtmlFields(v)))
        return res
    }
    return node
}