<script setup lang="ts">
import type { FilterDefinition } from '~/typing'
import TabGroup from './TabGroup.vue';

const props = defineProps<{
    definitions: FilterDefinition[]
    filterKey: string
}>()

const emit = defineEmits<{
    'update:filterKey': [key: string]
}>()

/* 1. 排序 & 默认值 */
const sorted = computed(() =>
    props.definitions.sort((a, b) => a.order - b.order)
)

/* 2. 响应式值对象 */
const values = reactive<Record<string, string>>({})
sorted.value.forEach((f) => {
    const def = f.options.find((o) => o.isDefault)
    values[f.filterKey] = def?.value ?? ''
})

/* 3. 实时 key 计算 */
const innerKey = computed(() =>
    Object.entries(values)
        .filter(([, v]) => v)
        .map(([k, v]) => `${k}:${v}`)
        .join('|')
)

/* 4. 向父组件同步（初始化 + 变化） */
watch(
    innerKey,
    (k) => emit('update:filterKey', k),
    { immediate: true }
)
</script>

<template>
    <div class="w-full flex flex-col gap-6">
        <template v-for="f in sorted" :key="f.filterKey">
            <!-- Dropdown -->
            <div class="w-full flex gap-6" v-if="f.filterType === 'Dropdown'">
                <div class="w-full flex flex-col gap-1">
                    <div>{{ f.filterName }}:</div>
                    <USelect v-model="values[f.filterKey]" :items="f.options" size="lg" color="neutral" highlight variant="none" class="w-1/4 max-lg:w-full rounded-none" style="--tw-ring-color: #747479" />
                </div>
            </div>

            <!-- Tab -->
            <div class="w-full" v-else-if="f.filterType === 'Tabs'">
                <TabGroup v-model="values[f.filterKey]"
                    :items="f.options.map((o) => ({ label: o.label, value: o.value }))" class="hidden lg:block" />

                <div class="hidden max-lg:flex w-full flex-col gap-1">
                    <div>{{ f.filterName }}:</div>
                    <USelect v-model="values[f.filterKey]"
                        :items="f.options.map((o) => ({ label: o.label, value: o.value }))" size="lg" color="neutral"
                        variant="none" highlight class="w-1/4 max-lg:w-full rounded-none" />
                </div>
            </div>
        </template>
    </div>
</template>