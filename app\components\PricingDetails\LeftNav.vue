<template>
    <nav class="w-full overflow-y-auto">
        <h2 class="px-4 text-base mb-3 text-[#1a1a1a]">{{ serviceDisplayName }}</h2>
        <section v-for="(section, idx) in navigation" :key="section.group">
            <!-- 组标题 + 折叠按钮 -->
            <button
                class="flex items-center justify-between w-full px-4 text-[#1a1a1a] text-left text-base cursor-pointer uppercase tracking-wider mb-2"
                @click="toggle(idx)">
                <span>{{ section.group }}</span>
                <svg :class="{ 'rotate-180': openState[idx] }" class="w-4 h-4 transition-transform duration-200"
                    fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                </svg>
            </button>

            <!-- 用 grid 做高度动画 -->
            <div class="grid transition-[grid-template-rows] duration-300 ease-in-out"
                :class="openState[idx] ? 'grid-rows-[1fr]' : 'grid-rows-[0fr]'">
                <ul class="overflow-hidden">
                    <li v-for="item in section.articles" :key="item.title">
                        <!-- 外链 -->
                        <a v-if="isExternal(item.link)" :href="item.link" target="_blank" rel="noopener noreferrer"
                            class="block px-4 py-2 text-base  text-[#0078D4] hover:bg-[#dbe1ea] transition">
                            {{ item.title }}
                        </a>

                        <!-- 内链 -->
                        <NuxtLinkLocale v-else :to="item.link"
                            class="block px-4 py-2 text-base text-[#0078D4] hover:bg-[#dbe1ea] transition"
                            :class="{highlight: item.link.includes('/pricing/details')}"
                            active-class="font-medium bg-sky-100 text-sky-700">
                            {{ item.title }}
                        </NuxtLinkLocale>
                    </li>
                </ul>
            </div>

            <!-- 组间距 -->
            <div class="mb-2"></div>
        </section>
    </nav>
</template>

<script setup lang="ts">
export interface NavArticle {
    title: string
    link: string
}

export interface NavGroup {
    group: string
    articles: NavArticle[]
}

interface Props {
    serviceDisplayName: string;
    navigation: NavGroup[]
}

const { navigation } = defineProps<Props>()

/* ---------- 折叠状态 ---------- */
const openState = ref<boolean[]>([])
onMounted(() => {
    openState.value = navigation.map((_, idx) => idx === 0) // 默认第一组展开
})
function toggle(index: number) {
    openState.value[index] = !openState.value[index]
}

/* ---------- 工具函数 ---------- */
const isExternal = (link: string): boolean =>
    link.startsWith('http://') || link.startsWith('https://')
</script>

<style lang="css" scoped>
@reference "~/assets/css/main.css";
.highlight {
    @apply bg-[#0078D4] text-white hover:bg-[#0078D4]
}
</style>