{"ncei": {"title": "Azure Pricing", "subTitle": "Flexible purchase and pricing options for a variety of cloud solutions", "subTitle2": "√Billed by actual usage √Tiered billing √Discount for preview services", "serviceFamily": "Service Family", "serviceName": "Service Name", "productName": "Product Name", "location": "Location", "type": "Type", "skuName": "SKU", "term": "Term", "meterName": "Meter Name", "unitOfMeasure": "Unit Of Measure", "price": {"a": "Retail Price", "b": " (RMB, VAT excluded)"}, "effectiveStartDate": "Effective Date", "search": "Search", "reset": "Reset", "clickHere": "Click Here", "lastUpdate": "Last Update", "Export&Download": "Export and download (.xlsx)"}, "menu": {"pricing": "Products and Pricing", "documents": "Azure Documents >", "market": "Azure Market >", "support&plan": "Azure Support Plan >", "ospa": "OSPA", "21vca-e": "21VCA-E"}, "login-button": "Sign in to your Azure account", "home": {"meta": {"title": "Cloud Computing Services Operated by 21Vianet - Microsoft Azure", "keywords": "Azure 云计算, Azure 云平台, 智能云", "description": "Invent with purpose, save money, and empower your organization to more efficiently leverage Microsoft Azure, the flexible, open cloud computing platform."}, "banner": {"title": "Accelerate your ideas with AI", "button": "Explore Azure"}, "products": {"title": "Start putting your ideas into action with Azure products and services", "list": [{"name": "Virtual Machines", "desc": "Create Linux and Windows virtual machines (VMs) in seconds and reduce costs."}, {"name": "Azure SQL Managed Instance", "desc": "Modernize SQL Server applications with a managed, always-up-to-date SQL instance in the cloud."}, {"name": "Azure Machine Learning", "desc": "Use an enterprise-grade AI service for the end-to-end machine learning lifecycle."}, {"name": "Azure Kubernetes Service (AKS)", "desc": "Deploy and scale containers on managed Kubernetes."}, {"name": "Azure Front Door", "desc": "Modern cloud CDN that delivers optimized experiences to your users anywhere."}, {"name": "Azure Blob Storage", "desc": "Massively scalable and secure object storage for cloud-native workloads, archives, data lakes, high-performance computing, and machine learning."}, {"name": "Azure Synapse Analytics", "desc": "Accelerate time to insight across enterprise data warehouses and big data systems."}, {"name": "Azure Virtual Network Manager", "desc": "Centrally manage virtual networks in Azure from a single pane of glass"}], "button": "View all products"}, "case": {"title": "See how customers are innovating with Azure", "link": "Learn more >"}, "map": {"title": "Azure global infrastructure", "description": "Azure makes it easy to choose the datacenter and regions that are right for you and your customers.", "product-by-region": "Check product availability by region >", "datacenters": "Explore the globe >"}}, "pricing": {"banner": {"title": "Azure Pricing", "subtitle": "Flexible purchase and pricing options for a variety of cloud solutions", "features": ["Billed by actual usage", "Tiered billing", "Discount for preview services"]}, "title": "Pricing details of products", "description": {"message": "Azure as a global service, we use Coordinated Universal Time (UTC) to calculate the usage.The following prices apply simultaneously to the 21vianet Online Service Premium Agreement (OSPA) and the New Commerce Experience (21VCA-E).For more discounts and terms regarding the New Commerce Experience, you can {cLink} to learn more details.", "here": "Click Here"}, "leftnav": {"mobile": {"placeholder": "Pricing details"}}}, "legal": {"left": {"title": "Legal", "legal-info": "Legal Information", "overview": "Overview", "ostpt": "Subscription Agreement", "service-terms": "Services Terms", "offer-details": "Offer Details", "privacy-statement": "Privacy Statement", "sla": "Service Level Agreements"}}, "faq": {"expand_all": "Expand All", "collapse_all": "Collapse All"}, "footer": {"followus": "Follow us", "getsupport": "Get support fast", "menu": {"products-and-pricing": "Products and pricing", "azure-hybrid-benefit": "Azure Hybrid Benefit", "azure-documentation": "Azure Documentation >", "get-support": "Get support >", "azure-support-plans": "Azure support plans >", "21vca-e-playbook": "21VCA-E Playbook >", "azure-portal": "Azure portal >", "enterprise-portal": "Enterprise portal >", "account-center": "Account center >", "microsoft-azure": "Microsoft Azure >", "products-available-by-region": "Products available by region >", "azure-status": "Azure status >", "azure-geographies": "Azure geographies >", "service-level-agreement-sla": "Service-level agreement (SLA)", "legal-information": "Legal Information", "icp-filing": "ICP filing", "public-security-filing": "Public security filing", "privacy-statement": "Privacy statement >", "trust-center": "Trust Center >"}, "icp": "SH ICP Filing No.********-25", "gov": "PSB Filing No. **************", "privacy": "Privacy"}, "error": "Something went wrong, please refresh the page."}