<template>
    <div class="break-inside-avoid-column">
        <!-- 图标（可选） -->
        <img v-if="node.iconImg" :src="node.iconImg" :alt="node.name" class="w-10 h-10 object-contain mb-1" />

        <!-- 标题 -->
        <h2 class="text-2xl font-medium mb-1" v-text="node.name" />

        <!-- 子级 -->
        <div v-if="node.subCategories.length" class="mb-6">
            <div v-for="child in node.subCategories" :key="child.id" class="mb-3">
                <NuxtLinkLocale :to="`/pricing/details/${child.slug}`"
                    class="block text-base font-normal hover:text-[#0078D4]">
                    {{ child.name }}
                </NuxtLinkLocale>

                <div v-if="child.subCategories.length" class="mt-3">
                    <div v-for="value in child.subCategories">
                        <NuxtLinkLocale
                            :to="`/pricing/details/${value.slug}`"
                            class="block text-base font-normal ml-4 border-l ps-4 py-0.5 hover:text-[#0078D4]">
                            {{ value.name }}
                        </NuxtLinkLocale>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { NuxtLinkLocale } from '#components';
import type { CategoryNode } from '~/typing'

defineProps<{ node: CategoryNode }>()

const { locale } = useI18n()

</script>